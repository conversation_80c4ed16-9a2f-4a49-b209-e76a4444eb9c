{"name": "onlydiary", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-rekognition": "^3.821.0", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "web-push": "^3.6.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}