"use client"

import { useState, useRef, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { photoStorage } from "@/lib/supabase/storage"

interface PhotoUploadProps {
  entryId?: string
  onPhotosChange?: (photos: any[]) => void
  onAutoSave?: () => Promise<string | null> // Returns entry ID after auto-save
  hasUnsavedChanges?: boolean
}

export function PhotoUpload({ entryId, onPhotosChange, onAutoSave, hasUnsavedChanges }: PhotoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [photos, setPhotos] = useState<any[]>([])
  const [error, setError] = useState("")
  const [autoSaving, setAutoSaving] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supabase = createSupabaseClient()

  // Load existing photos when entryId changes
  useEffect(() => {
    if (entryId) {
      loadExistingPhotos()
    } else {
      setPhotos([])
    }
  }, [entryId])

  const loadExistingPhotos = async () => {
    if (!entryId) return

    setLoading(true)
    setError("")

    try {
      console.log('Loading photos for entry:', entryId)

      const { data: existingPhotos, error: loadError } = await supabase
        .from('photos')
        .select('*')
        .eq('diary_entry_id', entryId)
        .order('created_at', { ascending: true })

      if (loadError) {
        console.error('Error loading photos:', loadError)
        setError("Failed to load existing photos")
      } else {
        console.log('Loaded photos:', existingPhotos)
        setPhotos(existingPhotos || [])
        onPhotosChange?.(existingPhotos || [])
      }
    } catch (err) {
      console.error('Error loading photos:', err)
      setError("Failed to load existing photos")
    } finally {
      setLoading(false)
    }
  }



  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    if (photos.length + files.length > 20) {
      setError("Maximum 20 photos per entry")
      return
    }

    setUploading(true)
    setError("")

    try {
      // Step 1: Auto-save entry if needed
      let currentEntryId: string | undefined = entryId

      if (!currentEntryId && onAutoSave) {
        setAutoSaving(true)
        setError("Saving entry...")
        const savedEntryId = await onAutoSave()
        setAutoSaving(false)

        if (!savedEntryId) {
          throw new Error("Please add some content to your entry before uploading photos")
        }
        currentEntryId = savedEntryId
      } else if (!currentEntryId) {
        throw new Error("Entry must be saved before uploading photos")
      }

      // Step 2: Upload photos to the saved entry
      const uploadPromises = Array.from(files).map(async (file) => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          throw new Error(`${file.name} is not an image file`)
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          throw new Error(`${file.name} is too large (max 10MB)`)
        }

        // Create unique filename
        const fileExt = file.name.split('.').pop()
        const fileName = `${currentEntryId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`

        // Upload to Supabase Storage with enhanced retry logic
        const { error: uploadError } = await photoStorage.upload(fileName, file)

        if (uploadError) {
          throw new Error(`Failed to upload ${file.name}: ${uploadError.message}`)
        }

        // Get public URL
        const { data: { publicUrl } } = photoStorage.getPublicUrl(fileName)

        // Save photo as APPROVED immediately - user sees it right away
        const { data: photoData, error: photoError } = await supabase
          .from('photos')
          .insert({
            diary_entry_id: currentEntryId,
            url: publicUrl,
            alt_text: file.name,
            moderation_status: 'approved'
          })
          .select()
          .single()

        if (photoError) {
          throw new Error(`Failed to save photo record: ${photoError.message}`)
        }

        // AWS moderation runs in background - doesn't affect user experience
        fetch('/api/moderate-photo', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ photoId: photoData.id })
        }).catch(() => {
          // Ignore errors - photo is already approved and visible
        })

        return photoData
      })

      const uploadedPhotos = await Promise.all(uploadPromises)
      const newPhotos = [...photos, ...uploadedPhotos]
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)

    } catch (err: any) {
      setError(err.message || "Failed to upload photos")
    } finally {
      setUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemovePhoto = async (photoId: string) => {
    try {
      const { error } = await supabase
        .from('photos')
        .delete()
        .eq('id', photoId)

      if (error) {
        setError("Failed to remove photo")
        return
      }

      const newPhotos = photos.filter(photo => photo.id !== photoId)
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)
    } catch {
      setError("Failed to remove photo")
    }
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Photos</h3>
        <span className="text-sm text-gray-500">{photos.length}/20</span>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {autoSaving && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">💾 Auto-saving your entry...</p>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">Loading photos...</p>
        </div>
      )}

      {/* Upload Button */}
      <div className="mb-4">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || loading || photos.length >= 20}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {autoSaving ? "Saving entry..." : uploading ? "Uploading..." : "Add Photos"}
        </button>
        {!entryId && !onAutoSave && (
          <p className="text-xs text-gray-500 mt-1">
            Save your entry first to upload photos
          </p>
        )}
        {!entryId && onAutoSave && (
          <p className="text-xs text-gray-500 mt-1">
            Your entry will be auto-saved when you add photos
          </p>
        )}
      </div>

      {/* Photo Grid - Facebook style thumbnails */}
      {photos.length > 0 && (
        <div className="space-y-4">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={photo.url}
                alt={photo.alt_text}
                className="w-full max-h-80 object-contain bg-gray-50"
              />
              <button
                onClick={() => handleRemovePhoto(photo.id)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                ×
              </button>
              {photo.moderation_status === 'flagged' && (
                <div className="absolute bottom-2 left-2 bg-orange-500 text-white text-xs px-2 py-1 rounded">
                  Flagged for Review
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
