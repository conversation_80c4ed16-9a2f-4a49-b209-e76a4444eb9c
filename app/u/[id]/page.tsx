import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import Link from "next/link"
import { SubscribeButton } from "@/components/SubscribeButton"
import { WriterProfileClient } from "@/components/WriterProfileClient"

interface WriterPageProps {
  params: Promise<{
    id: string
  }>
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}


export default async function WriterPage({ params }: WriterPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()
  
  // Check if current user is authenticated and get subscription status
  const { data: { user } } = await supabase.auth.getUser()
  
  // Check if user has active subscription to this writer
  let hasActiveSubscription = false
  if (user) {
    const { data: subscription } = await supabase
      .from("subscriptions")
      .select("id, active_until")
      .eq("subscriber_id", user.id)
      .eq("writer_id", id)
      .gte("active_until", new Date().toISOString())
      .single()
    
    hasActiveSubscription = !!subscription
  }
  
  // Get writer data and their free entry
  const { data: writerData, error: writerError } = await supabase
    .rpc('get_writer_public_data', { writer_uuid: id })
  
  if (writerError || !writerData || writerData.length === 0) {
    notFound()
  }
  
  const writer = writerData[0]
  
  // Get all diary entries (free and locked) for this writer
  const { data: allEntries, error: entriesError } = await supabase
    .from("diary_entries")
    .select("id, title, body_md, is_free, created_at")
    .eq("user_id", id)
    .eq("is_hidden", false)
    .order("created_at", { ascending: false })

  if (entriesError) {
    console.error('Error fetching entries:', entriesError)
  }

  // Get all book projects for this writer
  const { data: projects, error: projectsError } = await supabase
    .from("projects")
    .select(`
      id,
      title,
      description,
      cover_image_url,
      genre,
      is_private,
      is_complete,
      price_type,
      price_amount,
      total_chapters,
      total_words,
      created_at,
      updated_at
    `)
    .eq("user_id", id)
    .eq("is_private", false)
    .order("updated_at", { ascending: false })

  if (projectsError) {
    console.error('Error fetching projects:', projectsError)
  }
  
  return (
    <WriterProfileClient
      writer={writer}
      diaryEntries={allEntries || []}
      projects={projects || []}
      hasActiveSubscription={hasActiveSubscription}
      isOwnProfile={user?.id === id}
    />
  )
}