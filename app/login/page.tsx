"use client"

import { useState, useTransition } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { LinkButton } from "@/components/ui/link-button"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const supabase = createSupabaseClient()



  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)

    // Trigger login
    supabase.auth.signInWithPassword({
      email: email.trim(),
      password: password.trim(),
    })

    // Redirect to dashboard - it has the logic to redirect subscribers to timeline
    window.location.href = '/dashboard'
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600">
            OnlyDiary
          </Link>
          <h1 className="text-2xl font-serif mt-4 text-gray-800">Welcome Back</h1>
          <p className="text-gray-600 font-serif mt-2">
            Sign in to your account to access your subscriptions
          </p>

        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="Your password"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <Button
            type="submit"
            isLoading={loading}
            className="w-full bg-gray-800 text-white hover:bg-gray-700"
          >
            Sign In
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Don&apos;t have an account?
          </p>
          <LinkButton href="/register" variant="outline" className="w-full">
            Create Account
          </LinkButton>
        </div>

        <div className="mt-6 text-center">
          <LinkButton href="/forgot-password" variant="ghost" className="text-gray-600 hover:text-gray-800 text-sm p-0 h-auto">
            Forgot your password?
          </LinkButton>
        </div>
      </div>
    </div>
  )
}