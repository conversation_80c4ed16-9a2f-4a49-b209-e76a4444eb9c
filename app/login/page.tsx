"use client"

import { useState, useTransition, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LinkButton } from "@/components/ui/link-button"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [connectionStatus, setConnectionStatus] = useState<string>("")
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const supabase = createSupabaseClient()

  // Test Supabase connection on component mount
  useEffect(() => {
    const testConnection = async () => {
      try {
        const { data, error } = await supabase.from('users').select('count').limit(1)
        if (error) {
          setConnectionStatus("Connection issue detected")
          console.error('Supabase connection test failed:', error)
        } else {
          setConnectionStatus("Connected")
          console.log('Supabase connection test successful')
        }
      } catch (err) {
        setConnectionStatus("Connection failed")
        console.error('Supabase connection test exception:', err)
      }
    }
    testConnection()
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)

    console.log('Attempting login with:', email)

    try {
      // Add timeout to prevent hanging
      const loginPromise = supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password.trim(),
      })

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Login timeout')), 30000)
      )

      const { data, error } = await Promise.race([loginPromise, timeoutPromise]) as any

      console.log('Login result:', { data, error })

      if (error) {
        console.error('Login error:', error)
        setError(error.message)
        setLoading(false)
      } else if (data?.user) {
        console.log('Login successful, user:', data.user.id)

        // Get user profile to determine redirect
        try {
          const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('role')
            .eq('id', data.user.id)
            .single()

          console.log('User profile:', profile)

          if (profileError) {
            console.error('Profile error:', profileError)
            // Default to dashboard if profile fetch fails
            router.push('/dashboard')
          } else {
            // Redirect based on user role
            if (profile.role === 'subscriber') {
              router.push('/timeline')
            } else {
              router.push('/dashboard')
            }
          }
        } catch (profileErr) {
          console.error('Profile fetch exception:', profileErr)
          router.push('/dashboard')
        }
      } else {
        setError("Login failed. Please try again.")
        setLoading(false)
      }
    } catch (err: any) {
      console.error('Login exception:', err)
      if (err.message === 'Login timeout') {
        setError("Login is taking too long. Please check your connection and try again.")
      } else {
        setError("Login failed. Please try again.")
      }
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600">
            OnlyDiary
          </Link>
          <h1 className="text-2xl font-serif mt-4 text-gray-800">Welcome Back</h1>
          <p className="text-gray-600 font-serif mt-2">
            Sign in to your account to access your subscriptions
          </p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="Your password"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <Button
            type="submit"
            isLoading={loading}
            className="w-full bg-gray-800 text-white hover:bg-gray-700"
          >
            Sign In
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Don&apos;t have an account?
          </p>
          <LinkButton href="/register" variant="outline" className="w-full">
            Create Account
          </LinkButton>
        </div>

        <div className="mt-6 text-center">
          <LinkButton href="/forgot-password" variant="ghost" className="text-gray-600 hover:text-gray-800 text-sm p-0 h-auto">
            Forgot your password?
          </LinkButton>
        </div>
      </div>
    </div>
  )
}