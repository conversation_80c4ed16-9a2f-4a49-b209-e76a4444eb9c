'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Search, Users, BookOpen, Heart, Bookmark, BookmarkCheck } from 'lucide-react'

interface Creator {
  id: string
  name: string
  bio?: string
  avatar?: string
  profile_picture_url?: string
  subscriber_count: number
  entry_count: number
  bookmark_count: number
  is_bookmarked?: boolean
}

export default function DiscoverPage() {
  const [creators, setCreators] = useState<Creator[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filter, setFilter] = useState<'all' | 'new' | 'popular'>('all')
  const [user, setUser] = useState<any>(null)
  const [bookmarkingId, setBookmarkingId] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkUser()
    loadCreators()
  }, [filter])

  const checkUser = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', authUser.id)
          .single()
        setUser(profile)
      }
    } catch (error) {
      console.error('Error checking user:', error)
    }
  }

  const loadCreators = async () => {
    setLoading(true)
    try {
      // Simple query first to debug
      let query = supabase
        .from('users')
        .select('id, name, bio, avatar, profile_picture_url, subscriber_count, entry_count')
        .eq('role', 'writer')

      if (filter === 'popular') {
        query = query.order('subscriber_count', { ascending: false })
      } else if (filter === 'new') {
        query = query.order('created_at', { ascending: false })
      } else {
        query = query.order('entry_count', { ascending: false })
      }

      const { data, error } = await query.limit(20)

      if (error) {
        console.error('Error loading creators:', error)
      } else {
        let creatorsWithBookmarks = (data || [])
          .filter(creator => (creator.entry_count || 0) > 0)
          .map(creator => ({
            ...creator,
            bookmark_count: 0,
            is_bookmarked: false
          }))

        // If user is logged in, apply filters
        if (user) {
          // First, exclude the current user from seeing themselves
          creatorsWithBookmarks = creatorsWithBookmarks.filter(creator =>
            creator.id !== user.id
          )

          try {
            const { data: bookmarks, error: bookmarkError } = await supabase
              .from('bookmarks')
              .select('creator_id')
              .eq('reader_id', user.id)

            const bookmarkedIds = new Set(bookmarks?.map(b => b.creator_id) || [])

            // Hide creators you're already following from discover page
            creatorsWithBookmarks = creatorsWithBookmarks.filter(creator =>
              !bookmarkedIds.has(creator.id)
            )
          } catch (bookmarkError) {
            console.log('Bookmarks table error:', bookmarkError)
          }
        }

        setCreators(creatorsWithBookmarks)
      }
    } catch (err) {
      console.error('Error loading creators:', err)
    } finally {
      setLoading(false)
    }
  }

  const toggleBookmark = async (creatorId: string) => {
    if (!user) {
      console.log('No user found for bookmark action')
      return
    }

    console.log('Toggling bookmark for creator:', creatorId, 'User:', user.id)
    setBookmarkingId(creatorId)

    try {
      const creator = creators.find(c => c.id === creatorId)
      if (!creator) {
        console.log('Creator not found:', creatorId)
        return
      }

      if (creator.is_bookmarked) {
        // Remove bookmark
        console.log('Removing bookmark...')
        const { data, error } = await supabase
          .from('bookmarks')
          .delete()
          .eq('reader_id', user.id)
          .eq('creator_id', creatorId)

        console.log('Remove bookmark result:', { data, error })

        if (!error) {
          setCreators(prev => prev.map(c =>
            c.id === creatorId
              ? { ...c, is_bookmarked: false, bookmark_count: Math.max(0, c.bookmark_count - 1) }
              : c
          ))
          console.log('Bookmark removed successfully')
        } else {
          console.error('Error removing bookmark:', error)
        }
      } else {
        // Add bookmark
        console.log('Adding bookmark...')
        const { data, error } = await supabase
          .from('bookmarks')
          .insert({ reader_id: user.id, creator_id: creatorId })

        console.log('Add bookmark result:', { data, error })

        if (!error) {
          setCreators(prev => prev.map(c =>
            c.id === creatorId
              ? { ...c, is_bookmarked: true, bookmark_count: c.bookmark_count + 1 }
              : c
          ))
          console.log('Bookmark added successfully')
        } else {
          console.error('Error adding bookmark:', error)

          if (error.message?.includes('unique constraint')) {
            // Already bookmarked - update UI to reflect this
            setCreators(prev => prev.map(c =>
              c.id === creatorId
                ? { ...c, is_bookmarked: true }
                : c
            ))
            console.log('Creator was already bookmarked, updated UI')
          } else {
            alert(`Bookmark error: ${error.message || JSON.stringify(error)}`)
          }
        }
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error)
      alert('Something went wrong. Please try again.')
    } finally {
      setBookmarkingId(null)
    }
  }

  const filteredCreators = creators.filter(creator =>
    creator.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    creator.bio?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-6 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Discover Creators
          </h1>
          <p className="text-gray-600 font-serif">
            Find authentic voices sharing their most personal stories
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search creators by name or bio..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              All Creators
            </button>
            <button
              onClick={() => setFilter('popular')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'popular'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Most Popular
            </button>
            <button
              onClick={() => setFilter('new')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'new'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Newest
            </button>
          </div>
        </div>

        {/* Creators Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
                <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-4"></div>
                <div className="flex justify-center gap-4">
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredCreators.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCreators.map((creator) => (
              <div
                key={creator.id}
                className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 relative group"
              >
                {/* Follow Button */}
                {user && (
                  <button
                    onClick={(e) => {
                      e.preventDefault()
                      toggleBookmark(creator.id)
                    }}
                    disabled={bookmarkingId === creator.id}
                    className={`absolute top-4 right-4 p-3 rounded-full transition-all duration-300 transform hover:scale-110 bg-white text-gray-600 border-2 border-gray-200 hover:border-blue-400 hover:text-blue-600 shadow-md ${
                      bookmarkingId === creator.id ? 'animate-pulse' : ''
                    }`}
                  >
                    {bookmarkingId === creator.id ? (
                      <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Bookmark className="w-5 h-5" />
                    )}
                  </button>
                )}

                <Link href={`/u/${creator.id}`} className="block">
                  <div className="text-center">
                    <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4 overflow-hidden">
                      {creator.avatar || creator.profile_picture_url ? (
                        <img
                          src={creator.avatar || creator.profile_picture_url}
                          alt={creator.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-2xl font-serif text-gray-500">
                          {creator.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                  
                  <h3 className="text-lg font-serif text-gray-800 mb-2">
                    {creator.name}
                  </h3>
                  
                  {creator.bio && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {creator.bio}
                    </p>
                  )}
                  
                    <div className="flex justify-center gap-6 text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{creator.subscriber_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        <span>{creator.entry_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        <span>{creator.bookmark_count || 0}</span>
                      </div>
                    </div>



                    <div className="flex gap-2">
                      <div className="bg-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-blue-700 transition-colors flex-1 text-center">
                        View Profile
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Follow Action */}
                {user && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        toggleBookmark(creator.id)
                      }}
                      disabled={bookmarkingId === creator.id}
                      className="w-full py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
                    >
                      {bookmarkingId === creator.id ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Following...</span>
                        </>
                      ) : (
                        <>
                          <Bookmark className="w-4 h-4" />
                          <span>Follow</span>
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-serif text-gray-800 mb-2">No creators found</h3>
            <p className="text-gray-600">
              {searchQuery
                ? 'Try adjusting your search terms'
                : user
                ? 'You\'re already following all available creators! Check back later for new writers.'
                : 'No creators match the current filter'
              }
            </p>
            {user && !searchQuery && (
              <div className="mt-4">
                <Link
                  href="/timeline"
                  className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  📖 Go to Timeline
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
