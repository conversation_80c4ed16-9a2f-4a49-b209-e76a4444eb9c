"use client"

import { useState, useEffect, useTransition } from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"

export function Navigation() {
  const [user, setUser] = useState<any>(null)
  const [isPending, startTransition] = useTransition()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [navigating, setNavigating] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser()
        console.log('Navigation: Auth user:', authUser)

        if (authUser) {
          const { data: profile } = await supabase
            .from('users')
            .select('*')
            .eq('id', authUser.id)
            .single()

          console.log('Navigation: Profile:', profile)
          setUser(profile || { id: authUser.id, email: authUser.email, role: 'writer' })
        } else {
          setUser(null)
        }
      } catch (error) {
        console.log('Navigation: Error getting user:', error)
        setUser(null)
      }
    }

    getUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Navigation: Auth state change:', event, session?.user?.id)

      if (session?.user) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()

        console.log('Navigation: Profile from auth change:', profile)
        setUser(profile || { id: session.user.id, email: session.user.email, role: 'writer' })
      } else {
        setUser(null)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  // Reset navigation loading when route changes
  useEffect(() => {
    setNavigating(null)
  }, [pathname])

  const handleNavClick = (path: string) => {
    setNavigating(path)
    router.push(path)

    // Reset loading state after 3 seconds as fallback
    setTimeout(() => {
      setNavigating(null)
    }, 3000)
  }

  const handleSignOut = () => {
    startTransition(async () => {
      try {
        await supabase.auth.signOut()
        // Force immediate redirect
        window.location.href = '/'
      } catch (error) {
        console.error('Sign out error:', error)
        // Force redirect anyway
        window.location.href = '/'
      }
    })
  }

  return (
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600 transition-colors">
              OnlyDiary
            </Link>
          </div>

          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => handleNavClick('/trending')}
              disabled={navigating === '/trending'}
              className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 cursor-pointer disabled:opacity-50"
            >
              {navigating === '/trending' ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                  Loading...
                </>
              ) : (
                '🔥 Trending'
              )}
            </button>

            {user ? (
              <>
                {user?.role === 'subscriber' ? (
                  <>
                    <button
                      onClick={() => handleNavClick('/discover')}
                      disabled={navigating === '/discover'}
                      className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 cursor-pointer disabled:opacity-50"
                    >
                      {navigating === '/discover' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </>
                      ) : (
                        'Discover'
                      )}
                    </button>
                    <button
                      onClick={() => handleNavClick('/timeline')}
                      disabled={navigating === '/timeline'}
                      className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 cursor-pointer disabled:opacity-50"
                    >
                      {navigating === '/timeline' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </>
                      ) : (
                        'Timeline'
                      )}
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => handleNavClick('/dashboard')}
                      disabled={navigating === '/dashboard'}
                      className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 cursor-pointer disabled:opacity-50"
                    >
                      {navigating === '/dashboard' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </>
                      ) : (
                        'Dashboard'
                      )}
                    </button>
                    <button
                      onClick={() => handleNavClick('/write')}
                      disabled={navigating === '/write'}
                      className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 cursor-pointer disabled:opacity-50"
                    >
                      {navigating === '/write' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </>
                      ) : (
                        'Create'
                      )}
                    </button>
                  </>
                )}

                <div className="relative group">
                  <button className="flex items-center text-gray-600 hover:text-gray-800 font-medium">
                    {user.name || 'Profile'}
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Profile
                    </Link>
                    <button
                      onClick={handleSignOut}
                      disabled={isPending}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      {isPending ? 'Signing out...' : 'Sign Out'}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <>
                <button
                  onClick={() => handleNavClick('/login')}
                  disabled={navigating === '/login'}
                  className="text-gray-600 hover:text-gray-800 font-medium flex items-center gap-2 disabled:opacity-50 cursor-pointer"
                >
                  {navigating === '/login' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      Loading...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </button>
                <button
                  onClick={() => handleNavClick('/register')}
                  disabled={navigating === '/register'}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50 cursor-pointer"
                >
                  {navigating === '/register' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Loading...
                    </>
                  ) : (
                    'Get Started'
                  )}
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-gray-600 hover:text-gray-800 p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
              <Link
                href="/trending"
                className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                🔥 Trending
              </Link>

              {user ? (
                <>
                  {user?.role === 'subscriber' ? (
                    <>
                      <Link
                        href="/discover"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Discover
                      </Link>
                      <Link
                        href="/timeline"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Timeline
                      </Link>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/dashboard"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                      <Link
                        href="/write"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Create
                      </Link>
                    </>
                  )}

                  <Link
                    href="/profile"
                    className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <button
                    onClick={() => {
                      handleSignOut()
                      setMobileMenuOpen(false)
                    }}
                    disabled={isPending}
                    className="block w-full text-left py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                  >
                    {isPending ? 'Signing out...' : 'Sign Out'}
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/register"
                    className="block py-3 px-4 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Get Started
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
        </div>
      </nav>
  )
}
