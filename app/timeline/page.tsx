'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Heart, MessageCircle, Clock, Bookmark } from 'lucide-react'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  loves_count: number
  comments_count: number
  has_access: boolean
  credits_required: number
  is_recent?: boolean
  days_old?: number
}

interface UserCredits {
  [userId: string]: {
    credits_remaining: number
    last_reset: string
  }
}

export default function TimelinePage() {
  const [entries, setEntries] = useState<DiaryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [readingEntryId, setReadingEntryId] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadEntries()
  }, [])

  const loadEntries = async () => {
    try {
      // Get current user
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (!authUser) {
        setLoading(false)
        return
      }

      // Get user's bookmarks
      const { data: bookmarks } = await supabase
        .from('bookmarks')
        .select('creator_id')
        .eq('reader_id', authUser.id)

      if (!bookmarks || bookmarks.length === 0) {
        // No bookmarks = empty timeline
        setEntries([])
        setLoading(false)
        return
      }

      const creatorIds = bookmarks.map(b => b.creator_id)

      // Get entries from bookmarked creators, sorted by likes then date
      const { data } = await supabase
        .from('diary_entries')
        .select(`
          id,
          title,
          body_md,
          created_at,
          is_free,
          love_count,
          user_id,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url
          )
        `)
        .in('user_id', creatorIds)
        .eq('is_hidden', false)
        .order('love_count', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(50)

      // Mix entries by creator (not all from one person)
      const entriesByCreator = {}
      data?.forEach(entry => {
        if (!entriesByCreator[entry.user_id]) {
          entriesByCreator[entry.user_id] = []
        }
        entriesByCreator[entry.user_id].push(entry)
      })

      // Interleave entries from different creators
      const mixedEntries = []
      const maxPerCreator = Math.ceil(50 / creatorIds.length)

      for (let i = 0; i < maxPerCreator; i++) {
        Object.values(entriesByCreator).forEach((creatorEntries: any[]) => {
          if (creatorEntries[i]) {
            mixedEntries.push(creatorEntries[i])
          }
        })
      }

      setEntries(mixedEntries.slice(0, 50))
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }



  const handleReadEntry = (entryId: string) => {
    setReadingEntryId(entryId)
    window.location.href = `/d/${entryId}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">Timeline</h1>
          <p className="text-gray-600 font-serif">Latest stories from creators</p>
        </div>

        {entries.length > 0 ? (
          <div className="space-y-6">
            {entries.map((entry) => (
              <div key={entry.id} className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                    {entry.user?.avatar || entry.user?.profile_picture_url ? (
                      <img
                        src={entry.user.avatar || entry.user.profile_picture_url}
                        alt={entry.user.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-500">
                        {entry.user?.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-gray-900">{entry.user?.name || 'Unknown'}</h3>
                      <span className="text-gray-400">•</span>
                      <span className="text-gray-500 text-sm">
                        {formatDate(entry.created_at)}
                      </span>
                    </div>

                    <h4 className="text-lg font-serif text-gray-800 mb-3">{entry.title}</h4>
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {entry.body_md?.slice(0, 200)}...
                    </p>

                    <button
                      onClick={() => handleReadEntry(entry.id)}
                      disabled={readingEntryId === entry.id}
                      className="inline-block bg-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {readingEntryId === entry.id ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </div>
                      ) : (
                        'Read Entry'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-2xl p-12 text-center shadow-sm">
            <h3 className="text-xl font-serif text-gray-800 mb-2">No Stories Yet</h3>
            <p className="text-gray-600 mb-6">No entries found</p>
            <Link
              href="/discover"
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
            >
              Discover Creators
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
